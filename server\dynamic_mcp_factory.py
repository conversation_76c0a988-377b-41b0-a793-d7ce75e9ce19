#!/usr/bin/env python3
"""
Dynamic MCP Factory - Creates MCP servers and agents on-demand
Based on Multi-Agent RAG architecture with automatic data type detection
"""
import asyncio
import json
import logging
import os
import tempfile
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
import threading
import subprocess
import time
import shutil
import aiohttp
import sys
import tempfile
from datetime import datetime

from mcp.server.fastmcp import FastMCP
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import StateGraph
from langgraph.prebuilt import create_react_agent
from server.document_processor import document_processor

import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DynamicMCPFactory")

class EnhancedDataSourceDetector:
    """Enhanced detector for all data types shown in architecture diagram"""
    
    @staticmethod
    def detect_source_type(data_input: Union[str, Dict, List], file_content: bytes = None) -> Dict[str, Any]:
        """
        Enhanced detection for all data types from architecture diagram
        
        Args:
            data_input: User provided data (file path, connection string, raw data)
            file_content: Raw file content for uploaded files
            
        Returns:
            Detection metadata with source type and configuration
        """
        detection_result = {
            "source_type": "unknown",
            "config": {},
            "tools_needed": [],
            "mcp_server_config": {},
            "agent_type": "generic"
        }
        
        if isinstance(data_input, str):
            # File path or connection string detection
            if os.path.isfile(data_input) or file_content:
                file_ext = Path(data_input).suffix.lower() if data_input else ""
                
                # Document files (PDF, Word, Excel, PPT, text, JSON, MD)
                if file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.json', '.md']:
                    detection_result.update({
                        "source_type": "document",
                        "file_type": file_ext,
                        "file_path": data_input,
                        "tools_needed": ["document_search", "document_query", "document_extract"],
                        "agent_type": "document_agent",
                        "mcp_server_config": {
                            "server_type": "document_processor",
                            "file_path": data_input,
                            "file_type": file_ext
                        }
                    })
                
                # Image files
                elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg']:
                    detection_result.update({
                        "source_type": "image",
                        "file_type": file_ext,
                        "file_path": data_input,
                        "tools_needed": ["image_analyze", "image_extract_text", "image_describe"],
                        "agent_type": "image_agent",
                        "mcp_server_config": {
                            "server_type": "image_processor",
                            "file_path": data_input,
                            "file_type": file_ext
                        }
                    })
                
                # Vector DB files (if it's a vector database export)
                elif file_ext in ['.vec', '.faiss', '.pkl']:
                    detection_result.update({
                        "source_type": "vector_db",
                        "file_type": file_ext,
                        "file_path": data_input,
                        "tools_needed": ["vector_search", "vector_similarity", "vector_query"],
                        "agent_type": "vector_agent",
                        "mcp_server_config": {
                            "server_type": "vector_processor",
                            "file_path": data_input,
                            "file_type": file_ext
                        }
                    })
            
            # Database connection strings
            elif any(db_type in data_input.lower() for db_type in ["mongodb://", "mongodb+srv://", "postgresql://", "postgres://", "mysql://", "sqlite://", "oracle://"]):
                if "mongodb" in data_input:
                    db_type = "mongodb"
                elif "postgres" in data_input:
                    db_type = "postgresql"
                elif "mysql" in data_input:
                    db_type = "mysql"
                elif "sqlite" in data_input:
                    db_type = "sqlite"
                elif "oracle" in data_input:
                    db_type = "oracle"
                else:
                    db_type = "sql"
                
                detection_result.update({
                    "source_type": "database",
                    "db_type": db_type,
                    "connection_string": data_input,
                    "tools_needed": ["db_query", "db_search", "db_schema", "db_tables"],
                    "agent_type": "database_agent",
                    "mcp_server_config": {
                        "server_type": f"{db_type}_processor",
                        "connection_string": data_input,
                        "db_type": db_type
                    }
                })
        
        elif isinstance(data_input, (dict, list)):
            # Raw JSON/structured data
            detection_result.update({
                "source_type": "structured_data",
                "data": data_input,
                "tools_needed": ["data_search", "data_query", "data_filter"],
                "agent_type": "data_agent",
                "mcp_server_config": {
                    "server_type": "structured_data_processor",
                    "data": data_input
                }
            })
        
        logger.info(f"Detected source type: {detection_result['source_type']} -> Agent: {detection_result['agent_type']}")
        return detection_result

class DynamicMCPServerGenerator:
    """Generates specialized MCP servers for different data types"""
    
    def __init__(self):
        self.active_servers = {}
        self.server_ports = {}
        self.port_counter = 9000
    
    def _get_next_port(self) -> int:
        """Get next available port for MCP server"""
        self.port_counter += 1
        return self.port_counter
    
    async def create_document_mcp_server(self, server_id: str, config: Dict) -> Dict:
        """Create MCP server for document processing (PDF, Word, Excel, etc.)"""
        port = self._get_next_port()
        file_path = config.get('file_path', '')
        file_type = config.get('file_type', '')

        try:
            # Process the document once
            text, detected_file_type = await document_processor.process_document(file_path)
        
            # Store the processed text for the server to use
            processed_content = {
                'text': text,
                'file_type': detected_file_type,
                'original_path': file_path,
                'processed_at': datetime.utcnow().isoformat()
            }
        
            # Escape the processed content for embedding in Python code
            safe_text = json.dumps(text)
            safe_file_path = file_path.replace('\\', '\\\\').replace('"', '\\"')
        
            server_code = f'''import os
import json
import logging
from mcp.server.fastmcp import FastMCP

# Set the port as an environment variable
os.environ["MCP_PORT"] = "{port}"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DocumentProcessorServer")

# Initialize FastMCP
mcp = FastMCP("document_processor_{server_id}")

# Pre-processed document content
DOCUMENT_CONTENT = {safe_text}
FILE_PATH = r"{safe_file_path}"
FILE_TYPE = "{detected_file_type}"

@mcp.tool()
async def extract_text() -> dict:
    """Extract text content from the document"""
    try:
        return {{
            "status": "success",
            "text": DOCUMENT_CONTENT,
            "file_type": "{detected_file_type}",
            "char_count": len(DOCUMENT_CONTENT),
            "word_count": len(DOCUMENT_CONTENT.split())
        }}
    except Exception as e:
        logger.error(f"Error extracting text: {{str(e)}}", exc_info=True)
        return {{
            "status": "error",
            "error": str(e),
            "file_path": FILE_PATH
        }}

@mcp.tool()
async def search_document(query: str, limit: int = 5) -> dict:
    """Search for text in the document"""
    try:
        lines = DOCUMENT_CONTENT.split('\\n')
        results = []
        
        for i, line in enumerate(lines):
            if query.lower() in line.lower():
                results.append({{
                    "line_number": i + 1,
                    "content": line.strip(),
                    "context": "\\n".join(lines[max(0, i-1):min(len(lines), i+2)])
                }})
                if len(results) >= limit:
                    break
        
        return {{
            "status": "success",
            "results": results,
            "total_matches": len(results),
            "query": query
        }}
    except Exception as e:
        logger.error(f"Error searching document: {{str(e)}}", exc_info=True)
        return {{
            "status": "error",
            "error": str(e),
            "query": query
        }}

@mcp.tool()
async def analyze_document() -> dict:
    """Analyze document content and extract key information"""
    try:
        words = DOCUMENT_CONTENT.split()
        word_count = len(words)
        char_count = len(DOCUMENT_CONTENT)
        unique_words = len(set(words))
        avg_word_length = sum(len(word) for word in words) / word_count if word_count > 0 else 0
        
        return {{
            "status": "success",
            "file_type": "{detected_file_type}",
            "word_count": word_count,
            "char_count": char_count,
            "unique_words": unique_words,
            "avg_word_length": round(avg_word_length, 2),
            "analysis_summary": f"Document contains {{word_count}} words with {{unique_words}} unique words."
        }}
    except Exception as e:
        logger.error(f"Error analyzing document: {{str(e)}}", exc_info=True)
        return {{
            "status": "error",
            "error": str(e)
        }}

@mcp.tool()
async def extract_section(keywords: str) -> dict:
    """Extract sections containing specific keywords"""
    try:
        lines = DOCUMENT_CONTENT.split('\\n')
        keyword_list = [k.strip().lower() for k in keywords.split(',')]
        results = []
        
        for i, line in enumerate(lines):
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in keyword_list):
                results.append({{
                    "line_number": i + 1,
                    "content": line.strip(),
                    "context": "\\n".join(lines[max(0, i-1):min(len(lines), i+2)])
                }})
        
        return {{
            "status": "success",
            "keywords": keyword_list,
            "matches": results,
            "total_matches": len(results)
        }}
    except Exception as e:
        logger.error(f"Error extracting sections: {{str(e)}}", exc_info=True)
        return {{
            "status": "error",
            "error": str(e),
            "keywords": keywords
        }}

if __name__ == "__main__":
    logger.info(f"Starting document processor server for {{FILE_PATH}} on port {{os.environ.get('MCP_PORT', 'default')}}")
    mcp.run(transport="streamable-http")
'''
        
            # Store the processed content in the config for reference
            config['processed_content'] = processed_content
            return await self._create_and_start_server(server_id, server_code, port, config)
        
        except Exception as e:
            logger.error(f"Error creating document MCP server: {str(e)}", exc_info=True)
            raise

    async def create_database_mcp_server(self, server_id: str, config: Dict) -> Dict:
        """Create MCP server for database connections"""
        port = self._get_next_port()
        db_type = config.get('db_type', 'unknown')
        
        server_code = f'''
import asyncio
import json
from mcp.server.fastmcp import FastMCP
import logging

logging.basicConfig(level=logging.INFO)
mcp = FastMCP("database_processor_{server_id}")

CONNECTION_STRING = "{config['connection_string']}"
DB_TYPE = "{db_type}"

def get_db_connection():
    """Get database connection based on type"""
    try:
        if DB_TYPE == "mongodb":
            from pymongo import MongoClient
            return MongoClient(CONNECTION_STRING)
        elif DB_TYPE in ["postgresql", "postgres"]:
            import psycopg2
            return psycopg2.connect(CONNECTION_STRING)
        elif DB_TYPE == "mysql":
            import mysql.connector
            return mysql.connector.connect(CONNECTION_STRING)
        elif DB_TYPE == "sqlite":
            import sqlite3
            return sqlite3.connect(CONNECTION_STRING)
        else:
            return None
    except Exception as e:
        return None

@mcp.tool()
async def db_query(query: str, limit: int = 10) -> list:
    """Execute query on the database"""
    try:
        if DB_TYPE == "mongodb":
            client = get_db_connection()
            if client:
                db = client.get_default_database()
                # Simple text search across collections
                results = []
                for collection_name in db.list_collection_names():
                    collection = db[collection_name]
                    docs = list(collection.find({{"$text": {{"$search": query}}}}).limit(limit))
                    for doc in docs:
                        if '_id' in doc:
                            doc['_id'] = str(doc['_id'])
                        doc['_collection'] = collection_name
                        results.append(doc)
                return results
        else:
            # SQL databases
            conn = get_db_connection()
            if conn:
                cursor = conn.cursor()
                # Simple table listing for SQL databases
                if DB_TYPE == "sqlite":
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                else:
                    cursor.execute("SHOW TABLES;" if DB_TYPE == "mysql" else "SELECT tablename FROM pg_tables WHERE schemaname='public';")
                tables = cursor.fetchall()
                return [{{"table": table[0]}} for table in tables[:limit]]
        
        return [{{"error": "Could not connect to database"}}]
    except Exception as e:
        return [f"Error querying database: {{str(e)}}"]

@mcp.tool()
async def db_schema() -> dict:
    """Get database schema information"""
    try:
        if DB_TYPE == "mongodb":
            client = get_db_connection()
            if client:
                db = client.get_default_database()
                collections = db.list_collection_names()
                return {{"database_type": "MongoDB", "collections": collections}}
        else:
            conn = get_db_connection()
            if conn:
                return {{"database_type": DB_TYPE.upper(), "status": "connected"}}
        
        return {{"error": "Could not connect to database"}}
    except Exception as e:
        return {{"error": f"Error getting schema: {{str(e)}}"}}

@mcp.tool()
async def db_search(table: str, field: str, value: str) -> list:
    """Search specific field in database table/collection"""
    try:
        if DB_TYPE == "mongodb":
            client = get_db_connection()
            if client:
                db = client.get_default_database()
                collection = db[table]
                results = list(collection.find({{field: {{"$regex": value, "$options": "i"}}}}).limit(5))
                for result in results:
                    if '_id' in result:
                        result['_id'] = str(result['_id'])
                return results
        else:
            conn = get_db_connection()
            if conn:
                cursor = conn.cursor()
                query = f"SELECT * FROM {{table}} WHERE {{field}} LIKE '%{{value}}%' LIMIT 5;"
                cursor.execute(query)
                results = cursor.fetchall()
                return [list(row) for row in results]
        
        return [{{"error": "Could not search database"}}]
    except Exception as e:
        return [f"Error searching database: {{str(e)}}"]

if __name__ == "__main__":
    mcp.run(transport="streamable-http", port={port})
'''
        
        return await self._create_and_start_server(server_id, server_code, port, config)
    
    async def create_image_mcp_server(self, server_id: str, config: Dict) -> Dict:
        """Create MCP server for image processing"""
        port = self._get_next_port()
        
        server_code = f'''
import asyncio
import json
from mcp.server.fastmcp import FastMCP
import logging
from PIL import Image
import pytesseract
import base64
import io

logging.basicConfig(level=logging.INFO)
mcp = FastMCP("image_processor_{server_id}")

FILE_PATH = "{config['file_path']}"
FILE_TYPE = "{config['file_type']}"

@mcp.tool()
async def image_analyze() -> dict:
    """Analyze image properties and metadata"""
    try:
        with Image.open(FILE_PATH) as img:
            return {{
                "format": img.format,
                "mode": img.mode,
                "size": img.size,
                "width": img.width,
                "height": img.height,
                "file_type": FILE_TYPE
            }}
    except Exception as e:
        return {{"error": f"Error analyzing image: {{str(e)}}"}}

@mcp.tool()
async def image_extract_text() -> str:
    """Extract text from image using OCR"""
    try:
        with Image.open(FILE_PATH) as img:
            text = pytesseract.image_to_string(img)
            return text.strip()
    except Exception as e:
        return f"Error extracting text from image: {{str(e)}}"

@mcp.tool()
async def image_describe() -> str:
    """Provide basic description of the image"""
    try:
        with Image.open(FILE_PATH) as img:
            analysis = {{
                "dimensions": f"{{img.width}}x{{img.height}}",
                "format": img.format,
                "mode": img.mode,
                "estimated_colors": len(img.getcolors(maxcolors=256)) if img.getcolors(maxcolors=256) else "Many colors"
            }}
            
            # Try to extract text
            try:
                text = pytesseract.image_to_string(img)
                if text.strip():
                    analysis["contains_text"] = True
                    analysis["text_preview"] = text[:100] + "..." if len(text) > 100 else text
                else:
                    analysis["contains_text"] = False
            except:
                analysis["contains_text"] = "Unknown"
            
            return json.dumps(analysis, indent=2)
    except Exception as e:
        return f"Error describing image: {{str(e)}}"

if __name__ == "__main__":
    mcp.run(transport="streamable-http", port={port})
'''
        
        return await self._create_and_start_server(server_id, server_code, port, config)
    
    async def create_structured_data_mcp_server(self, server_id: str, config: Dict) -> Dict:
        """Create MCP server for structured data (JSON, etc.)"""
        port = self._get_next_port()
        
        server_code = f'''
import asyncio
import json
from mcp.server.fastmcp import FastMCP
import logging

logging.basicConfig(level=logging.INFO)
mcp = FastMCP("structured_data_processor_{server_id}")

DATA = {json.dumps(config['data'])}

@mcp.tool()
async def data_search(query: str, limit: int = 5) -> list:
    """Search within structured data"""
    try:
        results = []
        
        def search_recursive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{{path}}.{{key}}" if path else key
                    if query.lower() in str(key).lower() or query.lower() in str(value).lower():
                        results.append({{
                            "path": current_path,
                            "key": key,
                            "value": value,
                            "type": type(value).__name__
                        }})
                    if isinstance(value, (dict, list)) and len(results) < limit:
                        search_recursive(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{{path}}[{{i}}]" if path else f"[{{i}}]"
                    if query.lower() in str(item).lower():
                        results.append({{
                            "path": current_path,
                            "index": i,
                            "value": item,
                            "type": type(item).__name__
                        }})
                    if isinstance(item, (dict, list)) and len(results) < limit:
                        search_recursive(item, current_path)
        
        search_recursive(DATA)
        return results[:limit]
    except Exception as e:
        return [f"Error searching data: {{str(e)}}"]

@mcp.tool()
async def data_query(path: str = "") -> dict:
    """Query specific path in structured data"""
    try:
        if not path:
            return {{
                "data_type": type(DATA).__name__,
                "size": len(DATA) if isinstance(DATA, (list, dict)) else 1,
                "preview": str(DATA)[:200] + "..." if len(str(DATA)) > 200 else str(DATA)
            }}
        
        # Simple path navigation
        current = DATA
        for part in path.split('.'):
            if part.startswith('[') and part.endswith(']'):
                index = int(part[1:-1])
                current = current[index]
            else:
                current = current[part]
        
        return {{
            "path": path,
            "value": current,
            "type": type(current).__name__
        }}
    except Exception as e:
        return {{"error": f"Error querying data: {{str(e)}}"}}

@mcp.tool()
async def data_filter(criteria: str) -> list:
    """Filter data based on criteria"""
    try:
        results = []
        
        def filter_recursive(obj, path=""):
            if isinstance(obj, dict):
                # Simple filtering based on key-value pairs
                for key, value in obj.items():
                    if criteria.lower() in str(key).lower() or criteria.lower() in str(value).lower():
                        results.append({{
                            "path": f"{{path}}.{{key}}" if path else key,
                            "match": {{key: value}}
                        }})
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if criteria.lower() in str(item).lower():
                        results.append({{
                            "path": f"{{path}}[{{i}}]" if path else f"[{{i}}]",
                            "match": item
                        }})
        
        filter_recursive(DATA)
        return results[:10]  # Limit to 10 results
    except Exception as e:
        return [f"Error filtering data: {{str(e)}}"]

if __name__ == "__main__":
    mcp.run(transport="streamable-http", port={port})
'''
        
        return await self._create_and_start_server(server_id, server_code, port, config)
    
    async def _create_and_start_server(self, server_id: str, server_code: str, port: int, config: Dict) -> Dict:
        """Helper method to create and start MCP server with proper error handling"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(tempfile.gettempdir(), "mcp_servers")
            os.makedirs(temp_dir, exist_ok=True)
        
            # Write server code to temporary file
            server_file = os.path.join(temp_dir, f"dynamic_mcp_{server_id}.py")
            with open(server_file, 'w', encoding='utf-8') as f:
                f.write(server_code)
        
            # Start server process with proper error handling
            process = subprocess.Popen(
                [sys.executable, server_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
        
            # Wait for server to start with timeout
            max_attempts = 10
            server_url = f"http://127.0.0.1:{port}/mcp"
        
            for attempt in range(max_attempts):
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(f"{server_url}/openapi.json") as response:
                            if response.status == 200:
                                break
                except (aiohttp.ClientError, asyncio.TimeoutError):
                    await asyncio.sleep(1)
                
                # Check if process has terminated unexpectedly
                if process.poll() is not None:
                    stdout, stderr = process.communicate()
                    error_msg = f"Server process failed to start:\nStdout: {stdout}\nStderr: {stderr}"
                    logger.error(error_msg)
                    raise RuntimeError(f"Failed to start MCP server: {error_msg}")
            else:
                process.terminate()
                raise TimeoutError(f"Server did not become ready after {max_attempts} seconds")
        
            server_info = {
                "server_id": server_id,
                "port": port,
                "url": server_url,
                "process": process,
                "server_file": server_file,
                "config": config,
                "start_time": datetime.now().isoformat()
            }
        
            self.active_servers[server_id] = server_info
            self.server_ports[server_id] = port
        
            logger.info(f"Created and verified MCP server {server_id} on port {port}")
            return server_info
        
        except Exception as e:
            # Cleanup resources if something went wrong
            if 'process' in locals() and process.poll() is None:
                process.terminate()
            if 'server_file' in locals() and os.path.exists(server_file):
                try:
                    os.remove(server_file)
                except:
                    pass
            logger.error(f"Error creating MCP server: {str(e)}", exc_info=True)
            raise
    
    async def create_dynamic_server(self, detection_result: Dict) -> Dict:
        """Create appropriate MCP server based on detection result"""
        server_id = f"dynamic_{uuid.uuid4().hex[:8]}"
        source_type = detection_result["source_type"]
        config = detection_result["mcp_server_config"]
        
        if source_type == "document":
            return await self.create_document_mcp_server(server_id, config)
        elif source_type == "database":
            return await self.create_database_mcp_server(server_id, config)
        elif source_type == "image":
            return await self.create_image_mcp_server(server_id, config)
        elif source_type == "structured_data":
            return await self.create_structured_data_mcp_server(server_id, config)
        else:
            raise ValueError(f"Unsupported source type: {source_type}")
    
    def stop_server(self, server_id: str):
        """Stop and cleanup dynamic MCP server"""
        if server_id in self.active_servers:
            server_info = self.active_servers[server_id]
            server_info["process"].terminate()
            
            # Cleanup temp files
            if os.path.exists(server_info["server_file"]):
                os.remove(server_info["server_file"])
            
            del self.active_servers[server_id]
            del self.server_ports[server_id]
            logger.info(f"Stopped dynamic MCP server {server_id}")

class DynamicMCPFactory:
    """Main factory implementing the Multi-Agent RAG architecture"""
    
    def __init__(self, aggregator_agent):
        self.detector = EnhancedDataSourceDetector()
        self.server_generator = DynamicMCPServerGenerator()
        self.aggregator_agent = aggregator_agent
        self.dynamic_agents = {}  # user_id -> agent_info
        self.mcp_clients = {}     # user_id -> mcp_client
        self.user_workflows = {}  # user_id -> workflow_info
    
    async def create_dynamic_workflow(self, user_id: str, data_input: Union[str, Dict, List], 
                                    file_content: bytes = None) -> Dict:
        """
        Create complete dynamic MCP workflow following the architecture diagram
        
        Args:
            user_id: User identifier
            data_input: User provided data
            file_content: Raw file content for uploads
            
        Returns:
            Workflow information with agent and server details
        """
        try:
            logger.info(f"Creating dynamic workflow for user {user_id}")
            
            # Step 1: Detect data source type using enhanced detector
            detection_result = self.detector.detect_source_type(data_input, file_content)
            
            if detection_result["source_type"] == "unknown":
                raise ValueError("Could not detect data source type")
            
            # Step 2: Create specialized MCP server
            server_info = await self.server_generator.create_dynamic_server(detection_result)
            
            # Step 3: Create MCP client for the new server
            client_config = {
                f"dynamic_{server_info['server_id']}": {
                    "url": server_info["url"],
                    "transport": "streamable_http"
                }
            }
            
            mcp_client = MultiServerMCPClient(client_config)
            self.mcp_clients[user_id] = mcp_client
            
            # Step 4: Create specialized agent based on data type
            await self._create_specialized_agent(user_id, server_info, mcp_client, detection_result)
            
            # Step 5: Register with Master Agent (Aggregator)
            await self._register_with_master_agent(user_id, server_info, detection_result)
            
            # Step 6: Store workflow information
            workflow_info = {
                "user_id": user_id,
                "server_info": server_info,
                "detection_result": detection_result,
                "agent_type": detection_result["agent_type"],
                "status": "active",
                "created_at": datetime.now().isoformat(),
                "tools_available": detection_result["tools_needed"]
            }
            
            self.user_workflows[user_id] = workflow_info
            
            logger.info(f"Created dynamic workflow: {detection_result['agent_type']} for user {user_id}")
            return workflow_info
            
        except Exception as e:
            logger.error(f"Error creating dynamic workflow: {e}")
            raise
    
    async def _create_specialized_agent(self, user_id: str, server_info: Dict, mcp_client: MultiServerMCPClient, detection_result: Dict):
        """Create specialized agent based on data type (Agent 1, 2, 3 from diagram)"""
        try:
            logger.info(f"Creating specialized agent for user {user_id}")
        
            # Get tools from MCP client
            logger.info("Getting tools from MCP client...")
            try:
                tools = await mcp_client.get_tools()
                logger.info(f"Available tools: {[t.name for t in tools]}")
            except Exception as e:
                logger.error(f"Error getting tools from MCP client: {str(e)}")
                raise

            # Create specialized agent based on type
            from langchain_openai import ChatOpenAI
            from langchain.agents import Tool
            from langchain.agents import AgentExecutor
            from langchain.agents.openai_functions_agent.base import OpenAIFunctionsAgent
            from langchain.schema.messages import SystemMessage
            from langchain.prompts import MessagesPlaceholder
        
            logger.info(f"Initializing ChatOpenAI for user {user_id}")
            try:
                llm = ChatOpenAI(
                    model="gpt-3.5-turbo",
                    temperature=0,
                    api_key=os.getenv("OPENAI_API_KEY")
                )
            except Exception as e:
                logger.error(f"Error initializing ChatOpenAI: {str(e)}")
                raise

            agent_type = detection_result["agent_type"]
            source_type = detection_result["source_type"]
        
            # Create tools list
            tool_objects = []
            for tool in tools:
                try:
                    tool_objects.append(Tool(
                        name=tool.name,
                        func=tool.func,
                        description=tool.description
                    ))
                    logger.info(f"Added tool: {tool.name}")
                except Exception as e:
                    logger.error(f"Error creating tool {tool.name}: {str(e)}")
                    raise

            # Specialized prompts for different agent types
            prompts = {
                "document_agent": f"""You are Agent 1 - Document Processing Specialist for user {user_id}.
                You specialize in analyzing and extracting information from documents (PDF, Word, Excel, etc.).
                Your data source: {source_type} file
                Available tools: {[t.name for t in tool_objects]}
            
                Always provide detailed, accurate responses based on the document content.
                When searching, provide context and line numbers when possible.
                """
                # ... rest of the prompts
            }

            system_message = SystemMessage(content=prompts.get(agent_type, prompts["document_agent"]))
        
            logger.info("Creating agent executor...")
            try:
                agent = OpenAIFunctionsAgent.from_llm_and_tools(
                    llm=llm,
                    tools=tool_objects,
                    system_message=system_message,
                    extra_prompt_messages=[MessagesPlaceholder(variable_name="chat_history")],
                    verbose=True
                )
            
                agent_executor = AgentExecutor(
                    agent=agent,
                    tools=tool_objects,
                    verbose=True,
                    return_intermediate_steps=True,
                    max_iterations=5
                )
            
                # Store the agent
                self.dynamic_agents[user_id] = {
                    "agent": agent_executor,
                    "server_info": server_info,
                    "detection_result": detection_result,
                    "created_at": datetime.now().isoformat()
                }
            
                logger.info(f"Successfully created {agent_type} for user {user_id}")
                return agent_executor
            
            except Exception as e:
                logger.error(f"Error creating agent executor: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"Error in _create_specialized_agent: {str(e)}", exc_info=True)
            raise

    async def _register_with_master_agent(self, user_id: str, server_info: Dict, detection_result: Dict):
        """Register specialized agent with Master Agent (Aggregator)"""
        try:
            agent_key = f"dynamic_{user_id}"
            agent_type = detection_result["agent_type"]
            
            async def specialized_query_handler(query: str, **kwargs):
                """Handler for specialized agent queries"""
                if user_id in self.dynamic_agents:
                    agent_info = self.dynamic_agents[user_id]
                    agent = agent_info["agent"]
                    
                    logger.info(f"Invoking {agent_type} for user {user_id}")
                    
                    # Invoke specialized agent
                    response = await agent.ainvoke({
                        "messages": [{"role": "user", "content": query}]
                    })
                    
                    return {
                        "success": True,
                        "result": response["messages"][-1]["content"],
                        "source": f"{agent_type}_{server_info['server_id']}",
                        "user_id": user_id,
                        "agent_type": agent_type,
                        "tools_used": [tool.name for tool in agent_info["tools"]]
                    }
                else:
                    return {
                        "success": False,
                        "result": f"No {agent_type} found for user",
                        "source": "error"
                    }
            
            # Register with Master Agent (Aggregator)
            self.aggregator_agent.sub_agents[agent_key] = specialized_query_handler
            
            logger.info(f"Registered {agent_type} with Master Agent for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error registering with master agent: {e}")
            raise
    
    async def query_user_agent(self, user_id: str, query: str) -> Dict:
        """Query user's specialized agent through Master Agent"""
        try:
            agent_key = f"dynamic_{user_id}"
            
            if agent_key in self.aggregator_agent.sub_agents:
                result = await self.aggregator_agent.sub_agents[agent_key](query)
                logger.info(f"Query processed by {result.get('agent_type', 'unknown')} for user {user_id}")
                return result
            else:
                return {
                    "success": False,
                    "result": "No specialized agent found for this user",
                    "source": "error"
                }
                
        except Exception as e:
            logger.error(f"Error querying user agent: {e}")
            return {
                "success": False,
                "result": f"Error: {str(e)}",
                "source": "error"
            }
    
    def get_user_workflow_info(self, user_id: str) -> Optional[Dict]:
        """Get workflow information for user"""
        return self.user_workflows.get(user_id)
    
    def list_all_workflows(self) -> Dict:
        """List all active workflows"""
        return {
            "total_workflows": len(self.user_workflows),
            "active_servers": len(self.server_generator.active_servers),
            "workflows": list(self.user_workflows.values())
        }
    
    def cleanup_user_workflow(self, user_id: str):
        """Cleanup user's complete workflow"""
        try:
            # Stop MCP server
            if user_id in self.dynamic_agents:
                server_info = self.dynamic_agents[user_id]["server_info"]
                self.server_generator.stop_server(server_info["server_id"])
            
            # Remove from Master Agent
            agent_key = f"dynamic_{user_id}"
            if agent_key in self.aggregator_agent.sub_agents:
                del self.aggregator_agent.sub_agents[agent_key]
            
            # Cleanup local references
            if user_id in self.dynamic_agents:
                del self.dynamic_agents[user_id]
            
            if user_id in self.mcp_clients:
                del self.mcp_clients[user_id]
            
            if user_id in self.user_workflows:
                del self.user_workflows[user_id]
            
            logger.info(f"Cleaned up complete workflow for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up workflow: {e}")

# Global factory instance
dynamic_factory = None

def get_dynamic_factory(aggregator_agent):
    """Get or create dynamic factory instance"""
    global dynamic_factory
    if dynamic_factory is None:
        dynamic_factory = DynamicMCPFactory(aggregator_agent)
    return dynamic_factory

