# document_processor.py
import os
import logging
import asyncio
from typing import Op<PERSON>, Tuple, List
from pathlib import Path
from PIL import Image

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DocumentProcessor")

class DocumentProcessor:
    def __init__(self):
        self.supported_docs = ['.pdf', '.docx', '.doc', '.txt', '.md', '.jpg', '.jpeg', '.png', '.tiff', '.bmp']
        self.tesseract_available = self._check_tesseract()
        self.setup_tesseract_path()
        
    def setup_tesseract_path(self):
        """Set Tesseract executable path if not in system PATH"""
        try:
            import pytesseract
            # Common Tesseract installation paths on Windows
            tesseract_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME'))
            ]
            
            if not hasattr(pytesseract, 'get_tesseract_version'):
                for path in tesseract_paths:
                    if os.path.exists(path):
                        pytesseract.pytesseract.tesseract_cmd = path
                        logger.info(f"Tesseract found at: {path}")
                        break
                else:
                    logger.warning("Tesseract not found in common locations. Please ensure it's installed and in PATH")
        except Exception as e:
            logger.error(f"Error setting up Tesseract: {e}")

    def _check_tesseract(self) -> bool:
        """Check if Tesseract OCR is available"""
        try:
            import pytesseract
            pytesseract.get_tesseract_version()
            return True
        except (ImportError, pytesseract.TesseractNotFoundError) as e:
            logger.warning("Tesseract OCR is not installed or not in PATH. Install it for better document processing.")
            logger.debug(f"Tesseract error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error checking Tesseract: {e}")
            return False

    async def process_document(self, file_path: str) -> Tuple[str, str]:
        """
        Process a document and extract text
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Tuple of (extracted_text, file_type)
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
                
            file_type = file_path.suffix.lower()
            
            if file_type == '.pdf':
                return await self._process_pdf(file_path), file_type
            elif file_type in ['.docx', '.doc']:
                return await asyncio.to_thread(self._process_word, file_path), file_type
            elif file_type in ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']:
                return await self._process_image(file_path), file_type
            elif file_type in ['.txt', '.md']:
                return await asyncio.to_thread(self._process_text, file_path), file_type
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
            raise

    async def _process_pdf(self, file_path: Path) -> str:
        """Process PDF file with fallback to OCR if needed"""
        try:
            # First try to extract text directly
            from PyPDF2 import PdfReader
            
            with open(file_path, 'rb') as f:
                reader = PdfReader(f)
                text = ""
                for page in reader.pages:
                    page_text = page.extract_text() or ""
                    text += page_text + "\n"
                
                # If no text was extracted or text is too short, try OCR
                if not text.strip() or len(text.strip()) < 100:
                    logger.info("PDF appears to be image-based, attempting OCR...")
                    return await self._ocr_pdf(file_path)
                return text
                
        except Exception as e:
            logger.warning(f"Direct PDF extraction failed, falling back to OCR: {e}")
            return await self._ocr_pdf(file_path)

    async def _ocr_pdf(self, file_path: Path) -> str:
        """Extract text from PDF using OCR"""
        if not self.tesseract_available:
            raise RuntimeError("Tesseract OCR is required for processing image-based PDFs")
            
        try:
            import pytesseract
            from pdf2image import convert_from_path
            from concurrent.futures import ThreadPoolExecutor
            
            logger.info(f"Converting PDF pages to images: {file_path}")
            images = convert_from_path(file_path)
            text_parts = []
            
            # Process pages in parallel for better performance
            async def process_page(page_num: int, image):
                try:
                    logger.debug(f"Processing page {page_num + 1}/{len(images)}")
                    page_text = await asyncio.to_thread(
                        pytesseract.image_to_string, 
                        image,
                        timeout=60  # 60 seconds timeout per page
                    )
                    return f"\n\n--- Page {page_num + 1} ---\n{page_text}"
                except Exception as e:
                    logger.error(f"Error processing page {page_num + 1}: {e}")
                    return f"\n\n--- Page {page_num + 1} [Error: {str(e)}] ---\n"
            
            # Process pages with limited concurrency
            tasks = [process_page(i, img) for i, img in enumerate(images)]
            text_parts = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results, handling any exceptions
            return "".join(str(part) for part in text_parts)
            
        except ImportError as e:
            raise RuntimeError("Required packages not found. Install with: pip install pdf2image pytesseract")
        except Exception as e:
            logger.error(f"Error during PDF OCR: {e}", exc_info=True)
            raise

    def _process_word(self, file_path: Path) -> str:
        """Extract text from Word documents"""
        try:
            from docx import Document
            doc = Document(file_path)
            return "\n".join([para.text for para in doc.paragraphs if para.text.strip()])
        except ImportError:
            raise RuntimeError("python-docx is required for Word documents. Install with: pip install python-docx")
        except Exception as e:
            logger.error(f"Error processing Word document: {e}", exc_info=True)
            raise

    async def _process_image(self, file_path: Path) -> str:
        """Extract text from images using OCR"""
        if not self.tesseract_available:
            raise RuntimeError("Tesseract OCR is required for processing images")
            
        try:
            import pytesseract
            from PIL import Image, UnidentifiedImageError
            
            try:
                image = Image.open(file_path)
                # Convert to RGB if needed (Tesseract works best with RGB)
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Use asyncio.to_thread to avoid blocking the event loop
                return await asyncio.to_thread(
                    pytesseract.image_to_string, 
                    image,
                    timeout=30  # 30 seconds timeout
                )
            except UnidentifiedImageError:
                raise ValueError("The file is not a valid image or the image format is not supported")
                
        except Exception as e:
            logger.error(f"Error processing image with OCR: {e}", exc_info=True)
            raise

    def _process_text(self, file_path: Path) -> str:
        """Read plain text files with proper encoding handling"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'windows-1252', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, try with error handling
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()
                
        except Exception as e:
            logger.error(f"Error reading text file: {e}", exc_info=True)
            raise

# Create a global instance
document_processor = DocumentProcessor()