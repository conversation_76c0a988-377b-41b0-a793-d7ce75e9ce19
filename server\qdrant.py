import logging
from typing import Any
import httpx
from mcp.server.fastmcp import FastMCP
import os
from sentence_transformers import SentenceTransformer
import asyncio

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s %(message)s')

# Initialize MCP server
logging.info("Initializing FastMCP server with name 'qdrant'")
mcp = FastMCP("qdrant", redirect_slashes=False, stateless_http=True)

# Environment variables for config
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = os.environ.get("COLLECTION_NAME") 
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")

# Load embedding model
logging.info(f"Loading SentenceTransformer model: {EMBEDDING_MODEL}")
model = SentenceTransformer(EMBEDDING_MODEL)
logging.info("Model loaded successfully")

# Set headers
HEADERS = {
    "Authorization": f"Bearer {QDRANT_API_KEY}",
    "Content-Type": "application/json"
}

# Search Qdrant vector DB
async def search_qdrant_context(query: str, collection_name: str = COLLECTION_NAME, top_k: int = 5) -> list:
    logging.info(f"Searching Qdrant for query '{query}' in collection '{collection_name}' with top_k={top_k}")
    try:
        embedding = model.encode(query).tolist()
        payload = {
            "vector": embedding,
            "limit": top_k,
            "with_payload": True
        }
        url = f"{QDRANT_URL}/collections/{collection_name}/points/search"
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            results = response.json().get("result", [])
            if not results:
                logging.warning(f"No results found in Qdrant for query '{query}' in collection '{collection_name}'")
                return []
            formatted_results = [
                {
                    "text": item["payload"].get("content", ""),
                    "score": item.get("score", 0.0),
                    "metadata": {
                        "source": item["payload"].get("filename", "unknown_file"),
                        "collection": collection_name
                    }
                } for item in results
            ]
            logging.info(f"Qdrant search results: {formatted_results}")
            return formatted_results
    except Exception as e:
        logging.error(f"Error querying Qdrant: {str(e)}")
        return []

@mcp.tool()
async def qdrant_find(query: str, collection_name: str = COLLECTION_NAME) -> list:
    logging.info(f"Executing qdrant_find tool with query '{query}' and collection '{collection_name}'")
    return await search_qdrant_context(query, collection_name=collection_name, top_k=3)

# ✅ Main block
if __name__ == "__main__":
    import asyncio

    async def show_registered_tools():
        tools = await mcp.list_tools()
        print("REGISTERED TOOLS:", [tool.name for tool in tools])

    asyncio.run(show_registered_tools())

    logging.info("Starting MCP server")
    mcp.run(transport="streamable-http")
    logging.info("MCP server stopped")
