import logging
import async<PERSON>
import os
from dotenv import load_dotenv
from fastmcp import Client

# Load environment variables from .env file
load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("QdrantAgent")

# MCP server URL (where your qdrant1.py server is running)
QDRANT_MCP_URL = os.getenv("QDRANT_MCP_URL", "http://127.0.0.1:8000/mcp")
COLLECTION_NAME = os.getenv("COLLECTION_NAME", "Gen AI")

async def query_qdrant_tool(query: str, collection_name: str = None) -> str:
    """Query the Qdrant MCP tool using FastMCP Client."""
    if collection_name is None:
        collection_name = COLLECTION_NAME

    logger.info(f"Agent sending query: {query}")

    # Create FastMCP client - it will connect to the HTTP server
    QDRANT_MCP_URL = os.getenv("QDRANT_MCP_URL", "http://127.0.0.1:8000/mcp")
    client = Client(QDRANT_MCP_URL)

    try:
        async with client:
            logger.info("Connected to MCP server")

            # List available tools for debugging
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")

            # Call the qdrant_find tool
            result = await client.call_tool("qdrant_find", {
                "query": query,
                "collection_name": collection_name
            })

            logger.info(f"Tool call successful")
            logger.info(f"Raw result: {result}")

            # Handle the result - FastMCP returns a list of content items
            if not result:
                return "No relevant information found in Qdrant."

            # Format the results for display with enhanced source information
            formatted_results = []
            logger.info(f"Processing {len(result)} result items")

            for i, content_item in enumerate(result):
                if hasattr(content_item, 'text'):
                    # If it's a text content item
                    text_content = content_item.text
                    try:
                        # Try to parse as JSON if it contains structured data
                        import json
                        if text_content.startswith('[') or text_content.startswith('{'):
                            parsed_data = json.loads(text_content)
                            if isinstance(parsed_data, list):
                                for j, item in enumerate(parsed_data):
                                    if isinstance(item, dict):
                                        text = item.get("text", "").strip()
                                        score = item.get("score", 0.0)
                                        metadata = item.get("metadata", {})
                                        source_file = metadata.get("source", "unknown_file")
                                        collection = metadata.get("collection", collection_name)

                                        # Enhanced formatting with collection and file info
                                        formatted_results.append(
                                            f"{j+1}. {text} [Score: {score:.3f}] "
                                            f"Source: {source_file} | Collection: {collection}"
                                        )
                                        logger.info(f"Processed item {j+1}: score={score:.3f}, file={source_file}, collection={collection}")
                                    else:
                                        formatted_results.append(f"{j+1}. {str(item)}")
                            else:
                                # Single item result
                                if isinstance(parsed_data, dict):
                                    text = parsed_data.get("text", "").strip()
                                    score = parsed_data.get("score", 0.0)
                                    metadata = parsed_data.get("metadata", {})
                                    source_file = metadata.get("source", "unknown_file")
                                    collection = metadata.get("collection", collection_name)

                                    formatted_results.append(
                                        f"{i+1}. {text} [Score: {score:.3f}] "
                                        f"Source: {source_file} | Collection: {collection}"
                                    )
                                else:
                                    formatted_results.append(f"{i+1}. {str(parsed_data)}")
                        else:
                            formatted_results.append(f"{i+1}. {text_content}")
                    except Exception as parse_error:
                        logger.error(f"Error parsing result item {i}: {parse_error}")
                        # If parsing fails, just use the text as-is
                        formatted_results.append(f"{i+1}. {text_content}")
                else:
                    # If it's not a text content item, convert to string
                    formatted_results.append(f"{i+1}. {str(content_item)}")

            final_result = "\n\n".join(formatted_results) if formatted_results else "No relevant information found in Qdrant."
            logger.info(f"Final formatted result: {final_result[:200]}...")
            return final_result

    except Exception as e:
        logger.error(f"Error calling MCP tool: {e}")
        return f"Error: {str(e)}"

async def test_server_connection() -> bool:
    """Test if the MCP server is running and accessible."""
    try:
        client = Client(QDRANT_MCP_URL)
        async with client:
            await client.ping()
            logger.info("MCP server is running and accessible")
            return True
    except Exception as e:
        logger.error(f"Cannot connect to MCP server: {e}")
        return False

if __name__ == "__main__":
    async def main():
        # Test server connection first
        logger.info("Testing MCP server connection...")
        if not await test_server_connection():
            logger.error("MCP server is not accessible. Make sure qdrant1.py is running on port 8000")
            return

        # Query the tool
        #result = await query_qdrant_tool("Who is Sundar Pichai?")
        #print("Agent result:\n", result)

    asyncio.run(main())
