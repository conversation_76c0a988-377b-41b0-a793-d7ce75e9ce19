from fastapi import FastAP<PERSON>, HTTPException, Request
from pydantic import BaseModel
from typing import Optional
import uuid
from datetime import datetime
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
import requests
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
import os
import json
from typing import List, Optional, Dict, Any, AsyncGenerator
from langgraph.graph import StateGraph
from typing import List as TypingList
from datetime import datetime
import re
from langchain_mcp_adapters.client import MultiServerMCPClient
import logging
import redis
import ssl
import asyncio
from app.utils import handle_greeting
from app.guardrail import run_guardrails, output_guardrails, clean_llm_output


# Configure logging with more explicit formatting
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Ensure console output
    ]
)
logger = logging.getLogger(__name__)

# Force logging to be visible
logger.setLevel(logging.INFO)

# Load environment variables
load_dotenv()

# Configuration
MONGO_URL = os.getenv("MONGO_URL")
MONGO_DB = os.getenv("MONGO_DB", "spaarxsense")
MONGO_COLLECTION = os.getenv("MONGO_COLLECTION", "userconversations")
QDRANT_DEFAULT_COLLECTION = os.getenv("COLLECTION_NAME", "Gen AI")
MAX_TOKENS = 4096

# AWS Redis Configuration
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "SpaarxsenseAgenticRetrieval")
SESSION_TTL = int(os.getenv("SESSION_TTL", 900))  # 15 minutes default
REDIS_SSL = os.getenv("REDIS_SSL", "true").lower() == "true"

# Supported hosts
SUPPORTED_HOSTS = ["spaarxsenseaifabric", "groq", "google", "meta", "microsoft", "anthropic", "openai"]
SPAARXSENSE_AI_FABRIC_MODELS = ["deepseek-r1:1.5b"]

# Endpoints
SPAARXSENSE_AI_FABRIC_ENDPOINT = os.getenv("SPAARXSENSE_AI_FABRIC_ENDPOINT")
GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1/chat/completions")
GOOGLE_ENDPOINT = os.getenv("GOOGLE_ENDPOINT", "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent")
OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1/chat/completions")
HUGGINGFACE_ENDPOINT = os.getenv("HUGGINGFACE_ENDPOINT", "https://api-inference.huggingface.co/models")
ANTHROPIC_ENDPOINT = os.getenv("ANTHROPIC_ENDPOINT", "https://api.anthropic.com/v1/messages")

# MCP Server Configurations
QDRANT_MCP_URL = os.getenv("QDRANT_MCP_URL", "http://127.0.0.1:8000/mcp")
SERPER_MCP_URL = "https://google.serper.dev/search"

# Initialize FastAPI app
app = FastAPI(title="MCP Retrieval-SpaarxSense")

# Initialize Redis client with TLS
print("\n" + "="*60)
print("🔄 REDIS CONNECTION STATUS")
print("="*60)
logger.info(f"Redis Host: {REDIS_HOST}")
logger.info(f"Redis Port: {REDIS_PORT}")
logger.info(f"Redis SSL: {REDIS_SSL}")

redis_client = None
try:
    logger.info("🔗 Attempting Redis connection...")
   #ssl_context = ssl.create_default_context()
   #ssl_context.check_hostname = False
   #ssl_context.verify_mode = ssl.CERT_NONE

    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        db=REDIS_DB,
        ssl=REDIS_SSL,
        ssl_cert_reqs=None,
        ssl_check_hostname=False,
        decode_responses=True,
        socket_connect_timeout=10,
        socket_timeout=10,
        retry_on_timeout=True,
        ssl_min_version=ssl.TLSVersion.TLSv1_2  # Force TLS 1.2
    )

    response = redis_client.ping()
    logger.info(f"✅ Redis connection successful: PING = {response}")
    print("✅ Redis Status: CONNECTED")

except redis.AuthenticationError as e:
    logger.error(f"❌ Redis authentication failed: {str(e)}")
    print("❌ Redis Status: AUTHENTICATION FAILED")
    redis_client = None
except redis.ConnectionError as e:
    logger.error(f"❌ Redis connection failed: {str(e)}")
    print("❌ Redis Status: CONNECTION FAILED")
    redis_client = None
except Exception as e:
    logger.error(f"❌ Redis error: {str(e)}")
    print("❌ Redis Status: ERROR")
    redis_client = None

if redis_client is None:
    print("⚠️ Redis Status: RUNNING IN MONGODB-ONLY MODE")
print("="*60)

# Test AWS Redis connection
def test_redis_connection():
    logger.info("🔄 Testing Redis connection...")
    if redis_client is None:
        logger.warning("⚠️ Redis client not initialized - running in MongoDB-only mode")
        return False
    try:
        response = redis_client.ping()
        logger.info(f"✅ Connected to AWS Redis successfully: PING response = {response}")
        test_key = "health_check"
        redis_client.set(test_key, "ok", ex=10)
        test_value = redis_client.get(test_key)
        logger.info(f"✅ AWS Redis health check: set/get test successful - {test_value}")
        redis_client.delete(test_key)
        logger.info("🎉 Redis connection test completed successfully!")
        return True
    except redis.AuthenticationError as e:
        logger.error(f"❌ AWS Redis authentication failed: {str(e)}. Check authentication configuration.")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False
    except redis.ConnectionError as e:
        logger.error(f"❌ Failed to connect to AWS Redis: {str(e)}. Check network connectivity and security groups.")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error connecting to AWS Redis: {str(e)}")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False
    
# Test Qdrant connection
async def test_qdrant_connection():
    logger.info("🔄 Testing Qdrant connection...")
    try:
        qdrant_tool = await get_qdrant_tool()
        logger.info("✅ Qdrant MCP tool retrieved successfully")

        # Test with default collection
        test_result = await qdrant_tool.coroutine(query="test connection", collection_name=QDRANT_DEFAULT_COLLECTION)
        logger.info(f"✅ Qdrant connection test successful for collection '{QDRANT_DEFAULT_COLLECTION}'")
        logger.info("🎉 Qdrant connection test completed successfully!")
        return True
    except Exception as e:
        logger.error(f"❌ Qdrant connection test failed: {str(e)}")
        logger.warning(f"⚠️ Qdrant may not be available at {QDRANT_MCP_URL}")
        return False

# Initialize MCP client
print("\n" + "="*60)
print("🔄 QDRANT MCP CONNECTION STATUS")
print("="*60)
logger.info(f"Qdrant MCP URL: {QDRANT_MCP_URL}")
logger.info(f"Default Collection: {QDRANT_DEFAULT_COLLECTION}")

try:
    logger.info("🔗 Initializing Qdrant MCP client...")
    QDRANT_MCP_URL = os.getenv("QDRANT_MCP_URL", "http://127.0.0.1:8000/mcp")
    mcp_client = MultiServerMCPClient(
        {
            "qdrant": {
                "url": QDRANT_MCP_URL,
                "transport": "streamable_http"
            }
        }
    )
    logger.info("✅ Qdrant MCP client initialized successfully")
    print("✅ Qdrant MCP Status: CLIENT INITIALIZED")
except NameError as e:
    logger.error(f"❌ MultiServerMCPClient not found: {str(e)}")
    print("❌ Qdrant MCP Status: CLIENT NOT FOUND")
    raise HTTPException(status_code=500, detail=f"MultiServerMCPClient not found: {str(e)}")
except Exception as e:
    logger.error(f"❌ Qdrant MCP initialization error: {str(e)}")
    print("❌ Qdrant MCP Status: INITIALIZATION FAILED")
    raise HTTPException(status_code=500, detail=f"Error initializing MCP client: {str(e)}")

print("="*60)

# Test Qdrant MCP connection
async def test_qdrant_mcp_connection():
    try:
        logger.info("🔗 Testing Qdrant MCP connection...")
        tools = await mcp_client.get_tools()
        logger.info(f"✅ Available MCP tools: {[tool.name for tool in tools]}")

        qdrant_tool = next((tool for tool in tools if tool.name == "qdrant_find"), None)
        if not qdrant_tool:
            logger.error("❌ qdrant_find tool not found in MCP client")
            print("❌ Qdrant MCP Status: TOOL NOT FOUND")
            return False

        # Test with a simple query
        test_result = await qdrant_tool.coroutine(query="test", collection_name=QDRANT_DEFAULT_COLLECTION)
        logger.info(f"✅ Qdrant MCP test successful for collection '{QDRANT_DEFAULT_COLLECTION}'")
        print("✅ Qdrant MCP Status: CONNECTION SUCCESSFUL")
        return True
    except Exception as e:
        logger.error(f"❌ Qdrant MCP test failed: {str(e)}")
        print("❌ Qdrant MCP Status: CONNECTION FAILED")
        return False

# Test Serper connection
print("\n" + "="*60)
print("🔄 SERPER CONNECTION STATUS")
print("="*60)

async def test_serper_connection():
    try:
        from server.serper_agent import query_google_search

        logger.info("🔗 Initializing Serper connection...")
        logger.info(f"Serper API endpoint: https://google.serper.dev/search")

        # Test with a simple query
        test_result = await query_google_search("test query", 1)

        if "❌" in test_result or "Error" in test_result:
            logger.error(f"❌ Serper test failed: {test_result}")
            print("❌ Serper Status: CONNECTION FAILED")
            return False
        else:
            logger.info("✅ Serper connection test successful")
            print("✅ Serper Status: CONNECTION SUCCESSFUL")
            return True

    except Exception as e:
        logger.error(f"❌ Serper connection error: {str(e)}")
        print("❌ Serper Status: INITIALIZATION FAILED")
        return False

# Fetch qdrant_find tool
async def get_qdrant_tool():
    try:
        tools = await mcp_client.get_tools()
        logger.info(f"Available tools: {[tool.name for tool in tools]}")
        qdrant_tool = next((tool for tool in tools if tool.name == "qdrant_find"), None)
        if not qdrant_tool:
            logger.error("qdrant_find tool not found in MCP client")
            raise ValueError("qdrant_find tool not found")
        return qdrant_tool
    except Exception as e:
        logger.error(f"Error fetching tools: {str(e)}")
        raise 
    #HTTPException(status_code=500, detail=f"Error fetching Qdrant tools: {str(e)}")

# Initialize MongoDB
mongo_client = MongoClient(MONGO_URL)
db = mongo_client[MONGO_DB]
history_collection = db[MONGO_COLLECTION]

# Session management
active_sessions = {}

# Utility Functions
async def ensure_qdrant_collection(collection: str) -> bool:
    logger.info(f"Checking existence of Qdrant collection '{collection}'")
    try:
        qdrant_tool = await get_qdrant_tool()
        await qdrant_tool.coroutine(query="test", collection_name=collection)
        logger.info(f"Collection {collection} exists")
        return True
    except Exception as e:
        logger.warning(f"Collection {collection} does not exist: {str(e)}")
        return False

def get_user_id(request: Request) -> str:
    client_ip = request.client.host
    if client_ip not in active_sessions:
        active_sessions[client_ip] = {
            "user_id": str(ObjectId()),
            "current_chat_id": None,
            "last_accessed_history": None
        }
    return active_sessions[client_ip]["user_id"]

def is_valid_object_id(chat_id: str) -> bool:
    return bool(chat_id and isinstance(chat_id, str) and len(chat_id) == 24 and re.match(r'^[0-9a-fA-F]{24}$', chat_id))

def store_session_message(user_id: str, chat_id: str, message: dict):
    if redis_client is None:
        logger.debug("Redis not available, skipping session message storage")
        return
    try:
        session_key = f"session:{user_id}:{chat_id}"
        if 'timestamp' not in message:
            message['timestamp'] = datetime.now().isoformat()
        redis_client.rpush(session_key, json.dumps(message))
        redis_client.expire(session_key, SESSION_TTL)
        redis_client.ltrim(session_key, -10, -1)
        logger.info(f"Stored message in AWS Redis for session {session_key}")
    except redis.AuthenticationError as e:
        logger.error(f"AWS Redis authentication error during store_session_message: {str(e)}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during store_session_message: {str(e)}")
    except Exception as e:
        logger.error(f"Error storing message in AWS Redis: {str(e)}")

def store_session_metadata(user_id: str, chat_id: str, metadata: dict):
    if redis_client is None:
        logger.debug("Redis not available, skipping session metadata storage")
        return
    try:
        metadata_key = f"session_meta:{user_id}:{chat_id}"
        metadata_with_timestamp = metadata.copy()
        metadata_with_timestamp['updated_at'] = datetime.now().isoformat()
        for key, value in metadata_with_timestamp.items():
            redis_client.hset(metadata_key, key, str(value))
        redis_client.expire(metadata_key, SESSION_TTL)
        logger.info(f"Stored session metadata in AWS Redis for {metadata_key}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during store_session_metadata: {str(e)}")
    except Exception as e:
        logger.error(f"Error storing session metadata in AWS Redis: {str(e)}")

    
def get_session_metadata(user_id: str, chat_id: str) -> dict:
    if redis_client is None:
        logger.debug("Redis not available, returning empty metadata")
        return {}
    try:
        metadata_key = f"session_meta:{user_id}:{chat_id}"
        metadata = redis_client.hgetall(metadata_key)
        if metadata:
            redis_client.expire(metadata_key, SESSION_TTL)
            logger.debug(f"Retrieved session metadata from AWS Redis for {metadata_key}")
        return metadata
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during get_session_metadata: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"Error retrieving session metadata from AWS Redis: {str(e)}")
        return {}


def get_session_messages(user_id: str, chat_id: str, limit: int = 10) -> List[dict]:
    if redis_client is None:
        logger.debug("Redis not available, falling back to MongoDB")
        return get_mongodb_history_for_session(chat_id, limit)
    try:
        session_key = f"session:{user_id}:{chat_id}"
        messages = redis_client.lrange(session_key, -limit, -1)
        if messages:
            redis_client.expire(session_key, SESSION_TTL)
            session_messages = []
            for msg in messages:
                parsed_msg = json.loads(msg)
                if parsed_msg.get('metadata', {}).get('chat_id') == chat_id:
                    session_messages.append(parsed_msg)
                else:
                    logger.warning(f"Cross-session contamination detected in {session_key}")
            logger.debug(f"Retrieved {len(session_messages)} messages from Redis for session {session_key}")
            return session_messages
        logger.info(f"Redis session expired for {chat_id}, falling back to MongoDB")
        mongodb_messages = get_mongodb_history_for_session(chat_id, limit)
        if mongodb_messages and redis_client:
            for msg in mongodb_messages:
                store_session_message(user_id, chat_id, msg)
            logger.info(f"Restored {len(mongodb_messages)} messages to Redis from MongoDB")
        return mongodb_messages
    except redis.RedisError as e:
        logger.error(f"Redis error during get_session_messages: {str(e)}")
        return get_mongodb_history_for_session(chat_id, limit)
    
def get_mongodb_history_for_session(chat_id: str, limit: int = 10) -> List[dict]:
    try:
        chat_id_obj = ObjectId(chat_id)
        session_doc = history_collection.find_one({"$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]})
        if not session_doc:
            return []
        messages = session_doc.get('messages', [])
        return messages[-limit:] if len(messages) > limit else messages
    except Exception as e:
        logger.error(f"Error retrieving MongoDB history for session {chat_id}: {str(e)}")
        return []

def clear_session(user_id: str, chat_id: str):
    if redis_client is None:
        logger.debug("Redis not available, skipping session cleanup")
        return
    try:
        session_key = f"session:{user_id}:{chat_id}"
        metadata_key = f"session_meta:{user_id}:{chat_id}"
        deleted_count = redis_client.delete(session_key, metadata_key)
        logger.info(f"Cleared {deleted_count} keys from AWS Redis: {session_key} and {metadata_key}")
    except redis.AuthenticationError as e:
        logger.error(f"AWS Redis authentication error during clear_session: {str(e)}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during clear_session: {str(e)}")
    except Exception as e:
        logger.error(f"Error clearing session from AWS Redis: {str(e)}")

def get_session_history(chat_id: str) -> Dict:
    try:
        chat_id_obj = ObjectId(chat_id)
        session_doc = history_collection.find_one({"$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]})
        if not session_doc:
            raise HTTPException(status_code=404, detail=f"Chat ID {chat_id} not found in MongoDB.")
        processed_doc = {
            '_id': str(session_doc.get('_id', None)),
            'user': str(session_doc.get('user', None)) if session_doc.get('user') else None,
            'chatId': str(session_doc.get('chatId', None)) if session_doc.get('chatId') else None,
            'createdAt': str(session_doc.get('createdAt', None)) if session_doc.get('createdAt') else None,
            'updatedAt': str(session_doc.get('updatedAt', None)) if session_doc.get('updatedAt') else None,
            'title': session_doc.get('title', None),
            'analytics': {
                'lastViewedAt': str(session_doc['analytics'].get('lastViewedAt', None)) if session_doc.get('analytics', {}).get('lastViewedAt') else None,
                'views': session_doc.get('analytics', {}).get('views', 0),
                'exportCount': session_doc.get('analytics', {}).get('exportCount', 0),
                'searchCount': session_doc.get('analytics', {}).get('searchCount', 0)
            } if session_doc.get('analytics') else {'lastViewedAt': None, 'views': 0, 'exportCount': 0, 'searchCount': 0},
            'messages': [
                {
                    'id': str(msg.get('id', None)),
                    'role': msg.get('role', None),
                    'content': msg.get('content', None),
                    'timestamp': str(msg.get('timestamp', None)) if msg.get('timestamp') else None,
                    'metadata': msg.get('metadata', {}).copy() if isinstance(msg.get('metadata'), dict) else {}
                } for msg in session_doc.get('messages', []) if isinstance(msg, dict)
            ],
            'summary': session_doc.get('summary', None),
            'metadata': session_doc.get('metadata', {
                'totalMessages': 0,
                'totalTokensUsed': 0,
                'averageResponseTime': 0,
                'conversationDuration': 0,
                'topics': [],
                'sentiment': 'neutral',
                'complexity': 'simple'
            }),
            'status': session_doc.get('status', 'active'),
            'tags': session_doc.get('tags', []),
            'isBookmarked': session_doc.get('isBookmarked', False),
            'isShared': session_doc.get('isShared', True),
            'sharedWith': session_doc.get('sharedWith', []),
            '__v': session_doc.get('__v', 0)
        }
        if not any(processed_doc.values()):
            raise HTTPException(status_code=500, detail=f"Processed chat history for ID {chat_id} is empty.")
        return processed_doc
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid chat ID format: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching chat history: {str(e)}")

def append_to_chat_history(chat_id: str, query: str, response: str, user_id: str,
                          sources: List[str] = None, source_urls: List[str] = None,
                          mode: str = None, host: str = None, model: str = None):
    try:
        chat_id_obj = ObjectId(chat_id)
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

        user_message = {
            "id": str(ObjectId()),
            "role": "user",
            "content": query,
            "timestamp": current_time,
            "metadata": {
                "chat_id": chat_id,
                "mode": mode,
                "host": host,
                "model": model
            }
        }
        assistant_message = {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": response,
            "timestamp": current_time,
            "metadata": {
                "chat_id": chat_id,
                "sources": sources or [],
                "source_urls": source_urls or [],
                "mode": mode,
                "host": host,
                "model": model
            }
        }

        session_key = f"session:{user_id}:{chat_id}"
        
        # --- MODIFICATION START ---
        # Get the last message from Redis for this session
        last_msg_redis = None
        if redis_client:
            recent_msgs = redis_client.lrange(session_key, -1, -1)
            if recent_msgs:
                last_msg_redis = json.loads(recent_msgs[0])

        # Check if the last message was a user message with the same content
        if last_msg_redis and last_msg_redis.get('role') == 'user' and last_msg_redis.get('content') == user_message['content']:
            logger.info(f"Existing user message for chat_id={chat_id} found. Appending assistant response directly.")
            # If the user message is already there, just append the assistant message
            # This handles cases where the user message was already stored, and now the assistant response is ready.
            store_session_message(user_id, chat_id, assistant_message)
        else:
            # If it's a new turn (last message was assistant, or no messages, or different user query),
            # store both user and assistant messages
            store_session_message(user_id, chat_id, user_message)
            store_session_message(user_id, chat_id, assistant_message)
        # --- MODIFICATION END ---

        # MongoDB update (remains largely the same, as MongoDB is for long-term history)
        history_collection.update_one(
            {"$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]},
            {
                "$push": {
                    "messages": {
                        "$each": [user_message, assistant_message]
                    }
                },
                "$set": {
                    "updatedAt": current_time,
                    "metadata.totalMessages": {"$inc": {"totalMessages": 2}}
                }
            }
        )
    except Exception as e:
        logger.error(f"Error appending to chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error appending to chat history: {str(e)}")
    
def create_new_chat(user_id: str) -> str:
    try:
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        chat_id = str(ObjectId())
        history_collection.insert_one({
            "_id": ObjectId(chat_id),
            "user": user_id,
            "chatId": chat_id,
            "createdAt": current_time,
            "updatedAt": current_time,
            "title": "Chat",
            "analytics": {"agent": "WEB", "views": 1, "exportCount": 0, "searchCount": 0},
            "messages": [],
            "summary": "",
            "metadata": {
                "totalMessages": 0,
                "totalTokensUsed": 0,
                "averageResponseTime": 0,
                "conversationDuration": 0,
                "topics": [],
                "sentiment": "neutral",
                "complexity": "simple"
            },
            "status": "active",
            "tags": [],
            "isBookmarked": False,
            "isShared": True,
            "sharedWith": []
        })
        return chat_id
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating new chat: {str(e)}")

def validate_model(host: str, model: str, api_key: str) -> bool:
    try:
        headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        if host == "spaarxsenseaifabric":
            if model != "deepseek-r1:1.5b":
                logger.error(f"Only deepseek-r1:1.5b is supported for {host}, got: {model}")
                return False
            if not api_key:
                logger.error("API key is empty for spaarxsenseaifabric")
                return False
            if not re.match(r'^[a-zA-Z0-9]+$', api_key):
                logger.error(f"Invalid API key format for spaarxsenseaifabric: {api_key}")
                return False
            return True
        elif host == "groq":
            response = requests.get("https://api.groq.com/openai/v1/models", headers=headers)
            response.raise_for_status()
            models = [m["id"] for m in response.json().get("data", [])]
            return model in models
        elif host == "google":
            response = requests.get("https://generativelanguage.googleapis.com/v1/models", headers={"x-goog-api-key": api_key})
            response.raise_for_status()
            models = [m["name"].split("/")[-1] for m in response.json().get("models", [])]
            return model in models
        elif host == "openai":
            response = requests.get("https://api.openai.com/v1/models", headers=headers)
            response.raise_for_status()
            models = [m["id"] for m in response.json().get("data", [])]
            return model in models
        elif host == "meta" or host == "microsoft":
            return bool(re.match(r'^[a-zA-Z0-9\-]+/[a-zA-Z0-9\-]+$', model))
        elif host == "anthropic":
            return model.startswith("claude-")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Error validating model for {host}: {str(e)}")
        return False

# Request/Response Models
class FeedbackRequest(BaseModel):
    feedback_type: str  # e.g., 'thumbs_up', 'thumbs_down', 'rating', 'comment'
    feedback_value: Optional[Any] = None  # e.g., True/False, 1-5, string
    timestamp: Optional[str] = None  # ISO format, auto-set if not provided

class FeedbackResponse(BaseModel):
    status: str
    message: Optional[str] = None
    feedback: Optional[dict] = None

class QueryRequest(BaseModel):
    query: str
    chat_id: Optional[str] = None
    collections: List[str] = Field(default_factory=list)
    mode: Optional[str] = None
    host: Optional[str] = None
    api_key: Optional[str] = None
    model: Optional[str] = None
    data_source_config: Optional[Dict] = None

class QueryResponse(BaseModel):
    answer: str
    source: str
    source_urls: List[str] = Field(default_factory=list)
    documents: List[dict] = Field(default_factory=list)

class HistoryResponse(BaseModel):
    pass

class NewChatResponse(BaseModel):
    chat_id: str

# Import Multi-Agent RAG components
from aggregator_agent import get_aggregator_agent
from memory_manager1 import get_memory_manager
from planning_manager import get_planning_manager
from generative_model import get_generative_model

# Initialize Multi-Agent RAG components
print("\n" + "="*60)
print("🤖 MULTI-AGENT RAG INITIALIZATION")
print("="*60)

# Initialize memory manager with existing Redis and MongoDB connections
try:
    memory_manager = get_memory_manager(
        redis_client=redis_client,
        mongo_client=mongo_client,
        mongo_db=db,
        mongo_collection=history_collection
    )
    logger.info("✅ Memory manager initialized successfully")
except Exception as e:
    logger.error(f"❌ Error initializing memory manager: {e}")
    # Initialize with minimal configuration
    memory_manager = get_memory_manager(
        redis_client=redis_client,
        mongo_client=None,
        mongo_db=None,
        mongo_collection=None
    )

# Initialize planning manager
planning_manager = get_planning_manager()

# Initialize generative model
generative_model = get_generative_model()

# Initialize aggregator agent with memory and planning
aggregator_agent = get_aggregator_agent(memory_manager, planning_manager)

logger.info("✅ Multi-Agent RAG components initialized")
print("✅ Multi-Agent RAG Status: INITIALIZED")
print("="*60)

# Multi-Agent RAG Processing Function
async def process_multi_agent_query(query: str, mode: str, collections: List[str],
                                   host: str, model: str, api_key: str,
                                   chat_id: str, user_id: str, history: str = "") -> Dict[str, Any]:
    """
    Process query using Multi-Agent RAG architecture

    Args:
        query: User query
        mode: Query mode (rag, agentic, web)
        collections: Qdrant collections to search
        host: LLM host
        model: LLM model
        api_key: API key
        chat_id: Chat session ID
        user_id: User ID
        history: Chat history

    Returns:
        Multi-agent response
    """
    try:
        logger.info(f"🤖 Processing Multi-Agent query: '{query}' (mode: {mode})")

        # Step 1: Create execution plan
        plan = planning_manager.create_execution_plan(query, mode, collections)
        logger.info(f"📋 Execution plan created: {plan['mode']} mode, {len(plan['execution_steps'])} steps")

        # Step 2: Get memory context
        try:
            memory_context = memory_manager.get_context(chat_id, user_id, limit=10)
            logger.info(f"🧠 Memory context retrieved: {len(memory_context)} characters")
        except Exception as e:
            logger.error(f"❌ Error getting memory context: {e}")
            memory_context = ""

        # Step 3: Coordinate with sub-agents
        try:
            coordination_result = await aggregator_agent.coordinate_query(
                query=query,
                mode=mode,
                collections=collections
            )
            logger.info(f"🔄 Coordination complete: {coordination_result['decision']}")
        except Exception as e:
            logger.error(f"❌ Error in coordination: {e}")
            # Create fallback coordination result
            coordination_result = {
                "query": query,
                "mode": mode,
                "final_context": [f"Error in coordination: {str(e)}"],
                "documents": [],
                "source_urls": [],
                "decision": "error",
                "score": 0.0
            }

        # Step 4: Apply Chain of Thought reasoning
        reasoning = planning_manager.chain_of_thought_reasoning(query, coordination_result)
        logger.info(f"🧠 CoT reasoning: {reasoning['final_reasoning']}")

        # Step 5: Generate final response
        final_context = coordination_result.get("final_context", [])
        combined_history = f"{memory_context}\n{history}" if memory_context else history

        final_answer = await generative_model.generate_response(
            query=query,
            context=final_context,
            host=host,
            model=model,
            api_key=api_key,
            history=combined_history,
            mode=mode,
            stream=False
        )

        # Step 6: Validate response
        validated_answer = generative_model.validate_response(final_answer, mode, final_context)

        # Step 7: Store interaction in memory
        interaction_metadata = {
            "mode": mode,
            "host": host,
            "model": model,
            "decision": coordination_result["decision"],
            "score": coordination_result["score"],
            "plan_id": plan["plan_id"]
        }

        memory_manager.store_interaction(
            query=query,
            response=validated_answer,
            chat_id=chat_id,
            user_id=user_id,
            metadata=interaction_metadata
        )

        # Return comprehensive result with enhanced source information
        return {
            "answer": validated_answer,
            "source": coordination_result.get("source"),
            "documents": coordination_result.get("documents", []),
            "source_urls": coordination_result.get("source_urls", []),
            "score": coordination_result.get("score", 0.0),
            "decision": coordination_result.get("decision", ""),
            "decision_reason": coordination_result.get("decision_reason", ""),
            "collections_searched": coordination_result.get("collections_searched", collections),
            "final_context": coordination_result.get("final_context", []),
            "plan": plan,
            "reasoning": reasoning,
            "mode": mode
        }

    except Exception as e:
        logger.error(f"❌ Error in Multi-Agent processing: {e}")
        return {
            "answer": f"Error in Multi-Agent processing: {str(e)}",
            "source": "Error",
            "documents": [],
            "source_urls": [],
            "score": 0.0,
            "decision": "error",
            "mode": mode
        }


# LangGraph Flow Definition
from typing import TypedDict

class GraphState(TypedDict):
    query: str
    collections: List[str]
    host: str
    model: str
    api_key: str
    context: List[str]
    score: float
    web_context: List[str]
    final_context: List[str]
    answer: str
    documents: List[dict]
    mode: str
    chat_id: Optional[str]
    decision: str
    source_urls: List[str]
    history: str
    

# Legacy LangGraph Nodes (kept for backward compatibility)
async def qdrant_search_node(state):
    query = state["query"]
    collections_input = state.get("collections", [])
    if not collections_input or not any(c.strip() for c in collections_input if c):
        collections = [QDRANT_DEFAULT_COLLECTION]
        logger.info(f"No valid collections provided, using default: {QDRANT_DEFAULT_COLLECTION}")
    else:
        collections = [c.strip() for c in collections_input if c and c.strip()]
        if not collections:
            collections = [QDRANT_DEFAULT_COLLECTION]
            logger.info(f"All collections were empty, using default: {QDRANT_DEFAULT_COLLECTION}")

    logger.info(f"Processing collections: {collections}")

    combined_context = []
    best_score = 0.0
    files = []
    all_hits = []
    valid_collections_found = False

    try:
        qdrant_tool = await get_qdrant_tool()
        for collection in collections:
            if not await ensure_qdrant_collection(collection):
                logger.debug(f"Collection {collection} does not exist; skipping")
                continue
            valid_collections_found = True
            try:
                results = await qdrant_tool.coroutine(query=query, collection_name=collection)
                logger.info(f"Qdrant MCP tool results for collection {collection}: {results}")
                if isinstance(results, tuple):
                    results = results[0]
                if isinstance(results, str):
                    results = json.loads(results)
                if not isinstance(results, list):
                    logger.error(f"Unexpected Qdrant results format: {type(results)}")
                    continue
                for hit in results:
                    if isinstance(hit, str):
                        try:
                            hit = json.loads(hit.strip())
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse Qdrant hit: {hit}, error: {e}")
                            continue
                    if not isinstance(hit, dict):
                        logger.error(f"Invalid Qdrant hit format: {hit}")
                        continue
                    ctx = hit.get("text", "").strip()
                    filename = hit.get("metadata", {}).get("source", "unknown.pdf")
                    score = float(hit.get("score", 0.0))
                    if ctx:
                        all_hits.append({
                            "context": ctx,
                            "filename": filename,
                            "score": score,
                            "collection": collection
                        })
                        best_score = max(best_score, score)
                        files.append({"file": filename, "collection": collection})
            except Exception as e:
                logger.error(f"Error invoking qdrant_find for collection {collection}: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching qdrant_find tool: {str(e)}")
        return {**state, "context": [], "score": 0.0, "documents": [], "source_urls": []}

    if not valid_collections_found:
        logger.info("No valid Qdrant collections found; skipping Qdrant search")
        return {**state, "context": [], "score": 0.0, "documents": [], "source_urls": []}

    all_hits = sorted(all_hits, key=lambda x: x["score"], reverse=True)[:3]
    for hit in all_hits:
        combined_context.append(hit["context"])

    logger.info(f"Combined context: {combined_context}")
    logger.info(f"Best Qdrant score: {best_score}")
    logger.info(f"Documents: {files}")
    return {**state, "context": combined_context, "score": best_score, "documents": files, "source_urls": []}

def relevance_check_node(state):
    mode = state.get("mode", "agentic")
    score = state.get("score", 0.0)
    logger.info(f"Relevance check - Mode: {mode}, Score: {score}, Context length: {len(state.get('context', []))}")

    if mode == "rag":
        decision = "only_rag"
    elif mode == "web":
        decision = "force_web"
    elif mode == "agentic":
        decision = "needs_web_search" if score < 0.2 else "good_enough"
        logger.info(f"[Agentic] Score: {score} -> Decision: {decision}")
    else:
        decision = "only_rag"
    return {**state, "decision": decision, "documents": state.get("documents", []), "source_urls": []}

def search_serper_node(state):
    query = state["query"]
    source_urls = []
    web_context = []
    try:
        payload = {"q": query}
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": os.getenv("SERPER_API_KEY")
        }
        response = requests.post(SERPER_MCP_URL, json=payload, headers=headers)
        response.raise_for_status()
        result = response.json().get("organic", [])
        if result and isinstance(result, list):
            for item in result[:3]:
                if item.get('link'):
                    source_urls.append(item['link'])
            for item in result[:3]:
                snippet = f"{item.get('title', '')}: {item.get('snippet', '')} ({item.get('link', '')})"
                web_context.append(snippet)
    except requests.exceptions.RequestException as e:
        logger.error(f"Error with google_search: {str(e)}")
    return {**state, "web_context": web_context, "documents": [], "source_urls": source_urls}

def combine_context_node(state):
    mode = state.get("mode", "agentic")
    score = state.get("score", 0.0)
    qdrant_context = state.get("context", [])
    web_context = state.get("web_context", [])

    if mode == "agentic" and score >= 0.3 and qdrant_context:
        combined = qdrant_context
    elif mode == "agentic":
        if not qdrant_context and web_context:
            combined = web_context
        else:
            combined = qdrant_context + web_context
    elif mode == "web":
        combined = web_context
    else:
        combined = qdrant_context

    logger.info(f"Combined context length: {len(combined)}")
    return {
        **state,
        "final_context": combined,
        "documents": state.get("documents", []) if mode != "web" else [],
        "source_urls": state.get("source_urls", [])
    }


def generate_final_answer_node(state):
    host = state["host"]
    model = state["model"]
    api_key = state["api_key"]
    mode = state.get("mode", "agentic")
    context_list = state.get("final_context", state.get("context", []))
    context_str = "\n".join([f"- {c}" for c in context_list]) if context_list else "No relevant information found."
    source = state.get("source", "qdrant")
    source_urls = state.get("source_urls", [])
    history_str = state.get("history", "")

     # Special fallback logic for each mode
    if mode == "web":
        web_context = state.get("final_context", state.get("web_context", []))
        if web_context:
            answer = "\n".join(web_context)
        else:
            answer = "No web search results found."
        return {
            **state,
            "answer": answer,
            "documents": [],
            "source_urls": state.get("source_urls", [])
        }
    elif mode == "agentic":
        qdrant_score = state.get("score", 0.0)
        qdrant_context = state.get("final_context", state.get("context", []))
        web_context = state.get("web_context", [])
        if not qdrant_context or qdrant_score < 0.2:
            # Fallback to web
            if web_context:
                answer = "\n".join(web_context)
            else:
                answer = "No web search results found."
            return {
                **state,
                "answer": answer,
                "documents": [],
                "source_urls": state.get("source_urls", [])
            }
        # else: continue to normal answer generation using Qdrant context
    elif mode == "rag" and not context_list:
        logger.warning("No context found in rag mode; returning fallback response")
        return {
            **state,
            "answer": "Sorry, no relevant information was found in the provided documents.",
            "documents": [],
            "source_urls": []
        }

    prompt = f"""
You are a helpful AI assistant.

Response Instructions:

You are an intelligent assistant that answers user queries using only the information retrieved from context, chat history, or MCP tools. Do not use general knowledge.

- When mode is 'rag', use ONLY the provided Context from Qdrant documents to answer the query.
- In rag mode, if no Context is provided or Context is empty, respond EXACTLY with:
"Sorry, no relevant information was found in the provided documents."
- In rag mode, DO NOT use any general knowledge, outside information, or assumptions.
- When mode is 'agentic', use Context first; if insufficient, use web search results.
- When mode is 'web', use ONLY the provided web search results to answer the query.
- Base your response on the provided Context and History.

Answering Rules:
- Respond concisely and factually based on the Context/History and web search.
- Give the answer clean and accurate.
- Never use outside knowledge.
- Do not include any URLs inside the main answer.
- Ensure the answer is factual, clear and complete.
- Do not print this in my answer 'Based on the context and chat history'
- Do not include any Prompt Instructions in the final answer — only return the exact answer.
- Do not give any questions/querys in the final answer.


Context:
{context_str}

History:
{history_str}

Query:
{state['query']}

Your answer:
"""

    try:
        headers = {"Content-Type": "application/json"}
        if host == "spaarxsenseaifabric":
            llm_payload = {
                "model": model,
                "prompt": prompt,
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(SPAARXSENSE_AI_FABRIC_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("response", "No answer found.")
        elif host == "groq":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(GROQ_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("choices", [{}])[0].get("message", {}).get("content", "No answer found.")
        elif host == "google":
            llm_payload = {
                "contents": [{"parts": [{"text": prompt}]}]
            }
            headers["x-goog-api-key"] = api_key
            response = requests.post(GOOGLE_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "No answer found.")
        elif host == "meta":
            llm_payload = {"text": prompt}
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(f"{HUGGINGFACE_ENDPOINT}/{model}", json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data[0].get("generated_text", "No answer found.")
        elif host == "microsoft":
            llm_payload = {"text": prompt}
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(f"{HUGGINGFACE_ENDPOINT}/{model}", json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data[0].get("generated_text", "No answer found.")
        elif host == "anthropic":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": MAX_TOKENS
            }
            headers.update({
                "x-api-key": api_key,
                "anthropic-version": "2023-06-01"
            })
            response = requests.post(ANTHROPIC_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("content", [{}])[0].get("text", "No answer found.")
        elif host == "openai":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(OPENAI_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("choices", [{}])[0].get("message", {}).get("content", "No answer found.")
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported host: {host}")
        # Compose the structured source section for the answer
        source_section = ""
        if mode == "web" or (mode == "agentic" and not state.get("documents")):
            # Web search mode or agentic fallback to web
            source_section = "\n\nSource: web"
            urls = source_urls[:3]
            if urls:
                source_section += "\nSource URLs:"
                for url in urls:
                    source_section += f"\n- {url}"
        elif state.get("documents"):
            # Qdrant mode or agentic with Qdrant answer
            source_section = "\n\nSource: qdrant"
            docs = state.get("documents", [])
            if docs:
                source_section += "\nDocuments:"
                for doc in docs:
                    collection = doc.get("collection", "unknown_collection")
                    file = doc.get("file", doc.get("filename", "unknown_file"))
                    source_section += f"\n- Collection: {collection}\n  - File: {file}"
        else:
            # Fallback if no provenance
            source_section = "\n\nSource: unknown"

        return {
            **state,
            "answer": answer + source_section,
            "source": "web" if (mode == "web" or (mode == "agentic" and not state.get("documents"))) else "qdrant",
            "documents": state.get("documents", []) if mode != "web" else [],
            "source_urls": source_urls[:3] if (mode == "web" or (mode == "agentic" and not state.get("documents"))) else []
        }
    except requests.exceptions.RequestException as req_err:
        logger.error(f"LLM API request failed for host {host}: {str(req_err)}")
        answer = "Error connecting to the LLM service."
        return {
            **state,
            "answer": answer,
            "documents": [],
            "source_urls": []
        }


graph = StateGraph(GraphState)
graph.add_node("start", lambda state: state)
graph.add_node("qdrant_search", qdrant_search_node)
graph.add_node("relevance_check", relevance_check_node)
graph.add_node("search_serper", search_serper_node)
graph.add_node("combine_context", combine_context_node)
graph.add_node("generate_final_answer", generate_final_answer_node)

graph.add_edge("start", "qdrant_search")
graph.add_edge("qdrant_search", "relevance_check")
graph.add_conditional_edges(
    "relevance_check",
    lambda state: state["decision"],
    {
        "good_enough": "generate_final_answer",
        "needs_web_search": "search_serper",
        "only_rag": "generate_final_answer",
        "force_web": "search_serper",
    }
)
graph.add_edge("search_serper", "combine_context")
graph.add_edge("combine_context", "generate_final_answer")
graph.set_entry_point("start")
agent_graph = graph.compile()

# FastAPI Endpoints
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app):
    logger.info("🚀 Starting FastAPI application...")

    # Test connections during startup
    print("\n" + "="*60)
    print("🚀 FASTAPI STARTUP - TESTING CONNECTIONS")
    print("="*60)

    redis_status = test_redis_connection()
    qdrant_status = await test_qdrant_mcp_connection()
    serper_status = await test_serper_connection()

    # Final status summary
    print("\n" + "="*60)
    print("📊 FINAL CONNECTION STATUS SUMMARY")
    print("="*60)
    print(f"Redis:   {'✅ CONNECTED' if redis_status else '❌ DISCONNECTED'}")
    print(f"Qdrant:  {'✅ CONNECTED' if qdrant_status else '❌ DISCONNECTED'}")
    print(f"Serper:  {'✅ CONNECTED' if serper_status else '❌ DISCONNECTED'}")
    print("="*60)

    if redis_status and qdrant_status and serper_status:
        print("🎉 ALL SERVICES CONNECTED!")
        logger.info("🎉 FastAPI startup successful - All services connected!")
    elif redis_status or qdrant_status or serper_status:
        print("⚠️ PARTIAL CONNECTIVITY")
        logger.warning("⚠️ FastAPI startup with partial connectivity")
    else:
        print("⚠️ LIMITED CONNECTIVITY")
        logger.warning("⚠️ FastAPI startup with limited connectivity")

    yield

    # Cleanup during shutdown
    logger.info("🔄 Shutting down FastAPI application...")
    try:
        if redis_client:
            redis_client.close()
        logger.info("✅ AWS Redis connection closed")
    except Exception as e:
        logger.warning(f"⚠️ Error closing AWS Redis connection: {str(e)}")
    logger.info("👋 FastAPI application shutdown complete")

app = FastAPI(title="MCP Retrieval-SpaarxSense", lifespan=lifespan)

@app.post("/new_chat", response_model=NewChatResponse)
async def new_chat(request: Request):
    try:
        user_id = get_user_id(request)
        chat_id = create_new_chat(user_id)
        client_ip = request.client.host
        active_sessions[client_ip]["current_chat_id"] = chat_id
        active_sessions[client_ip]["last_accessed_history"] = chat_id
        logger.info(f"New chat created: chat_id={chat_id}, user_id={user_id}")
        return NewChatResponse(chat_id=chat_id)
    except Exception as e:
        logger.error(f"Error creating new chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating new chat: {str(e)}")

@app.get("/history/{chat_id}", response_model=HistoryResponse)
async def get_history(chat_id: str, request: Request):
    try:
        full_doc = get_session_history(chat_id)
        if not full_doc or not any(v for k, v in full_doc.items() if k != 'messages'):
            raise HTTPException(status_code=404, detail=f"Chat history for ID {chat_id} is empty or invalid.")
        client_ip = request.client.host
        user_id = get_user_id(request)
        if client_ip not in active_sessions:
            active_sessions[client_ip] = {
                "user_id": user_id,
                "current_chat_id": None,
                "last_accessed_history": None
            }
        active_sessions[client_ip]["current_chat_id"] = chat_id
        active_sessions[client_ip]["last_accessed_history"] = chat_id
        session_metadata = get_session_metadata(user_id, chat_id)
        if not session_metadata:
            messages = full_doc.get('messages', [])
            if messages:
                last_assistant_msg = None
                for msg in reversed(messages):
                    if msg.get('role') == 'assistant':
                        last_assistant_msg = msg
                        break
                if last_assistant_msg and last_assistant_msg.get('metadata'):
                    metadata = last_assistant_msg['metadata']
                    restored_metadata = {
                        'mode': metadata.get('mode', 'agentic'),
                        'host': metadata.get('host', ''),
                        'model': metadata.get('model', ''),
                        'restored_from_mongodb': 'true',
                        'last_query_time': datetime.now().isoformat()
                    }
                    store_session_metadata(user_id, chat_id, restored_metadata)
                    logger.info(f"Restored session metadata from MongoDB for chat_id={chat_id}")
        logger.info(f"Retrieved history for chat_id={chat_id}")
        return JSONResponse(content=full_doc)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching history for chat_id={chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching history: {str(e)}")
    
@app.post("/query")
async def query(request: Request, query_request: QueryRequest):
    try:
        client_ip = request.client.host
        user_id = get_user_id(request)
        chat_id = query_request.chat_id
        if not chat_id or not is_valid_object_id(chat_id):
            chat_id = active_sessions.get(client_ip, {}).get("current_chat_id")
            if not chat_id:
                chat_id = create_new_chat(user_id)
                active_sessions[client_ip] = {
                    "user_id": user_id,
                    "current_chat_id": chat_id,
                    "last_accessed_history": None
                }
                logger.info(f"Created new chat_id={chat_id} for user_id={user_id}")

        # Get session metadata
        session_metadata = get_session_metadata(user_id, chat_id)
        mode = query_request.mode or session_metadata.get('mode') or 'agentic'
        host = query_request.host or session_metadata.get('host')
        api_key = query_request.api_key or session_metadata.get('api_key')
        model = query_request.model or session_metadata.get('model')


        # Validation
        if not host:
            raise HTTPException(status_code=400, detail="Host is required for new sessions or when not provided in follow-up questions.")
        if not api_key:
            raise HTTPException(status_code=400, detail="API key is required for new sessions or when not provided in follow-up questions.")
        if not model:
            raise HTTPException(status_code=400, detail="Model is required for new sessions or when not provided in follow-up questions.")
        if host.lower() not in [h.lower() for h in SUPPORTED_HOSTS]:
            raise HTTPException(status_code=400, detail=f"Invalid host: {host}. Choose from: {', '.join(SUPPORTED_HOSTS)}")
        if not validate_model(host.lower(), model, api_key):
            raise HTTPException(status_code=400, detail=f"Model '{model}' is not supported by host '{host}'.")
        valid_modes = {"rag", "agentic", "web"}
        if mode.lower() not in valid_modes:
            raise HTTPException(status_code=400, detail="Invalid mode. Choose from: rag, agentic, web.")

        # Store session metadata
        current_metadata = {
            'mode': mode.lower(),
            'host': host.lower(),
            'api_key': api_key,
            'model': model,
            'last_query_time': datetime.now().isoformat()
        }
        store_session_metadata(user_id, chat_id, current_metadata)

        # Handle greetings with clean streaming
        greeting_response = handle_greeting(query_request.query.strip())
        if greeting_response:
            async def greeting_stream():
                words = greeting_response.split()
                for i, word in enumerate(words):
                    if i == 0:
                        yield word
                    else:
                        yield f" {word}"
                    await asyncio.sleep(0.05)
                yield "\n\nSource: greeting\n"
            return StreamingResponse(greeting_stream(), media_type="text/plain")

        # --- Enforce ALL guardrails before LLM processing ---
        is_safe, guardrail_result = run_guardrails(query_request.query, openai_api_key=api_key)
        if not is_safe:
            return StreamingResponse((chunk for chunk in [guardrail_result]), media_type="text/plain")
        
        # Get chat history for context
        recent_messages = memory_manager.get_session_specific_context(user_id, chat_id, limit=10)
        history_str = "\n".join([f"{msg['role'].capitalize()}: {msg['content']}" for msg in recent_messages])

        # 🤖 USE MULTI-AGENT RAG SYSTEM WITH STREAMING
        logger.info(f"🤖 Processing with Multi-Agent RAG: mode={mode}, host={host}, model={model}")

        state = {
            "query": query_request.query,
            "collections": query_request.collections or [QDRANT_DEFAULT_COLLECTION],
            "host": host,
            "model": model,
            "api_key": api_key,
            "mode": mode,
            "chat_id": chat_id,
            "user_id": user_id,
            "history": history_str,
            "context": [],
            "score": 0.0,
            "web_context": [],
            "final_context": [],
            "answer": "",
            "documents": [],
            "source_urls": [],
            "decision": "",
             }
        result = await agent_graph.ainvoke(state)

        # Extract simplified results
        documents = result.get("documents", [])
        source_urls = result.get("source_urls", [])[:3]
        base_source = result.get("source", "Multi-Agent RAG")


        # Keep source simple as requested
        if base_source == "qdrant":
            source = "qdrant"
        elif base_source == "web":
            source = "web"
        else:
            source = base_source

        # Prepare source information for storage
        sources = []
        if documents:
            sources.extend([doc.get('file', 'unknown') for doc in documents])
        if source_urls:
            sources.append("Web Search")

        # Always use streaming response with clean formatting
        async def generate_stream():
            try:
                # Get context for streaming
                final_context = result.get("final_context", [])
                combined_history = f"{memory_manager.get_context(chat_id, user_id)}\n{history_str}"

                # Stream the response cleanly (no data/chunk wrapper)
                full_response = ""
                async for chunk in generative_model.generate_streaming_response(
                    query=query_request.query,
                    context=final_context,
                    host=host.lower(),
                    model=model,
                    api_key=api_key,
                    history=combined_history,
                    mode=mode.lower()
                ):
                    full_response += chunk
                    yield chunk  # Direct output without JSON wrapper

                # Apply guardrails to full response
                cleaned_answer = clean_llm_output(full_response.strip())
                is_safe_out, output_guardrail_result = output_guardrails(cleaned_answer)
                display_response = guardrail_result if "safety" not in guardrail_result.lower() else cleaned_answer

                # Add source information in clean format (line by line)
                yield "\n\n"  # Two line breaks after answer
                yield f"Source: {source}\n"

                # Show documents if available
                if documents:
                    yield "Documents:\n"
                    for i, doc in enumerate(documents, 1):
                        yield f"  {i}. File name: {doc.get('file', 'unknown')}\n"
                        yield f"     Collection name: {doc.get('collection', 'unknown')}\n"

                # Show source URLs if available (one per line)
                if source_urls:
                    yield "Source URLs:\n"
                    for i, url in enumerate(source_urls, 1):
                        yield f"  {i}. {url}\n"

                # Store complete conversation in Redis with all details
                if chat_id:
                    # Store in chat history
                    append_to_chat_history(
                        chat_id,
                        query_request.query,
                        display_response,
                        user_id,
                        sources=sources,
                        source_urls=source_urls,
                        mode=mode.lower(),
                        host=host.lower(),
                        model=model
                    )

                    # Store detailed conversation data in Redis
                    conversation_data = {
                        "query": query_request.query,
                        "answer": display_response,
                        "source": source,
                        "source_urls": source_urls,
                        "documents": documents,
                        "mode": mode.lower(),
                        "host": host.lower(),
                        "model": model,
                        "chat_id": chat_id,
                        "timestamp": datetime.now().isoformat()
                    }
                    memory_manager.store_session_metadata(user_id, chat_id, conversation_data)

                logger.info(f"🎉 Multi-Agent query processed: query='{query_request.query}', chat_id={chat_id}, source={source}, mode={mode}")

            except Exception as e:
                logger.error(f"Error in streaming: {e}")
                yield f"data: {json.dumps({'chunk': f'Error: {str(e)}', 'done': True})}\n\n"

        return StreamingResponse(generate_stream(), media_type="text/plain")

    except requests.exceptions.RequestException as e:
        logger.error(f"Search API failed: {str(e)}")
        return QueryResponse(
            answer="Sorry, no relevant information was found due to server connection issues.",
            source="None",
            documents=[],
            source_urls=[],
        )
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Error processing query: {str(e)}",
            source="None",
            documents=[],
            source_urls=[],
        )
    
@app.get("/session_metadata/{chat_id}")
async def get_session_metadata_endpoint(chat_id: str, request: Request):
    try:
        user_id = get_user_id(request)
        metadata = get_session_metadata(user_id, chat_id)
        safe_metadata = {k: v for k, v in metadata.items() if k != 'api_key'}
        return {
            "chat_id": chat_id,
            "metadata": safe_metadata,
            "has_active_session": bool(metadata)
        }
    except Exception as e:
        logger.error(f"Error fetching session metadata for chat_id={chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching session metadata: {str(e)}")

    
@app.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(feedback: FeedbackRequest, request: Request):
    """
    Store user feedback for the most recent query/response in Redis, associated with session and user.
    """
    try:
        user_id = get_user_id(request)
        client_ip = request.client.host
        # Get current chat_id from active_sessions using client_ip
        chat_id = None
        if client_ip in active_sessions:
            chat_id = active_sessions[client_ip].get("current_chat_id")
        if not chat_id:
            raise HTTPException(status_code=400, detail="No active chat session found for feedback.")
        # Retrieve most recent query/answer for this session
        session_messages = get_session_messages(user_id, chat_id, limit=1)
        if not session_messages:
            raise HTTPException(status_code=404, detail="No query/answer found to attach feedback.")
        latest_msg = session_messages[-1]
        
        feedback_entry = {
            "feedback_type": feedback.feedback_type,
            "feedback_value": feedback.feedback_value,
            "timestamp": feedback.timestamp or datetime.now().isoformat(),
        }
        latest_msg["feedback"] = feedback_entry
        if redis_client is None:
            raise HTTPException(status_code=503, detail="Redis not available for feedback storage.")
        # Overwrite last message in session history with feedback attached
        session_key = f"session:{user_id}:{chat_id}"
        all_msgs = redis_client.lrange(session_key, 0, -1)
        if all_msgs:
            all_msgs = [json.loads(m) for m in all_msgs]
            all_msgs[-1] = latest_msg
            redis_client.delete(session_key)
            for m in all_msgs:
                redis_client.rpush(session_key, json.dumps(m))
            redis_client.expire(session_key, SESSION_TTL)
             # Also store feedback in a feedback key for analytics, but only feedback fields
        feedback_key = f"feedback:{user_id}:{chat_id}"
        redis_client.rpush(feedback_key, json.dumps(feedback_entry))
        redis_client.expire(feedback_key, SESSION_TTL)
        return FeedbackResponse(status="success", feedback=feedback_entry)
    except Exception as e:
        logger.error(f"Error storing feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error storing feedback: {str(e)}")

def get_feedback_for_session(user_id: str, chat_id: str, limit: int = 20) -> list:
    """
    Retrieve feedback entries for a session from Redis.
    """
    feedback_key = f"feedback:{user_id}:{chat_id}"
    if redis_client is None:
        return []
    try:
        feedbacks = redis_client.lrange(feedback_key, -limit, -1)
        return [json.loads(fb) for fb in feedbacks]
    except Exception as e:
        logger.error(f"Error retrieving feedback: {str(e)}")
        return []

@app.post("/end_session")
async def end_session(request: Request, chat_id: str):
    try:
        user_id = get_user_id(request)
        clear_session(user_id, chat_id)
        client_ip = request.client.host
        if client_ip in active_sessions:
            if active_sessions[client_ip].get("current_chat_id") == chat_id:
                active_sessions[client_ip]["current_chat_id"] = None
            if active_sessions[client_ip].get("last_accessed_history") == chat_id:
                active_sessions[client_ip]["last_accessed_history"] = None
        logger.info(f"Session ended: chat_id={chat_id}, user_id={user_id}")
        return {"message": f"Session {chat_id} cleared successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error clearing session for chat_id={chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing session: {str(e)}")

@app.get("/multi_agent_status")
async def multi_agent_status():
    """Get Multi-Agent RAG system status"""
    try:
        # Test aggregator agent
        aggregator_status = "initialized" if aggregator_agent else "not_initialized"

        # Test memory manager
        memory_stats = memory_manager.get_memory_stats() if memory_manager else {}

        # Test planning manager
        planning_history = planning_manager.get_planning_history(limit=5) if planning_manager else []

        # Test generative model
        generative_status = "initialized" if generative_model else "not_initialized"

        # Test sub-agents connectivity
        sub_agent_status = {}

        # Test Qdrant agent
        try:
            from server.qdrant_agent import query_qdrant_tool
            test_result = await query_qdrant_tool("test", "Gen AI")
            sub_agent_status["qdrant"] = "connected" if test_result else "disconnected"
        except Exception as e:
            sub_agent_status["qdrant"] = f"error: {str(e)}"

        # Test Serper agent
        try:
            from server.serper_agent import query_google_search
            test_result = await query_google_search("test", 1)
            sub_agent_status["serper"] = "connected" if test_result else "disconnected"
        except Exception as e:
            sub_agent_status["serper"] = f"error: {str(e)}"

        return {
            "multi_agent_rag": {
                "status": "operational",
                "aggregator_agent": aggregator_status,
                "memory_manager": memory_stats,
                "planning_manager": {
                    "status": "initialized" if planning_manager else "not_initialized",
                    "recent_plans": len(planning_history),
                    "last_plan": planning_history[-1]["created_at"] if planning_history else None
                },
                "generative_model": generative_status,
                "sub_agents": sub_agent_status
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "multi_agent_rag": {
                "status": "error",
                "error": str(e)
            },
            "timestamp": datetime.now().isoformat()
        }


@app.get("/redis_status")
async def redis_status():
    if redis_client is None:
        return {
            "status": "not_initialized",
            "message": "Redis client was not initialized - running in MongoDB-only mode",
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL
        }
    try:
        ping_result = redis_client.ping()
        info = redis_client.info()
        memory_info = {
            "used_memory": info.get("used_memory_human", "N/A"),
            "used_memory_peak": info.get("used_memory_peak_human", "N/A"),
            "connected_clients": info.get("connected_clients", "N/A"),
            "total_commands_processed": info.get("total_commands_processed", "N/A"),
            "keyspace_hits": info.get("keyspace_hits", "N/A"),
            "keyspace_misses": info.get("keyspace_misses", "N/A")
        }
        session_keys = redis_client.keys("session:*")
        metadata_keys = redis_client.keys("session_meta:*")
        return {
            "status": "connected",
            "ping": ping_result,
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "active_sessions": len(session_keys),
            "active_metadata": len(metadata_keys),
            "memory_info": memory_info,
            "redis_version": info.get("redis_version", "N/A"),
            "uptime_in_seconds": info.get("uptime_in_seconds", "N/A")
        }
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error in status check: {str(e)}")
        return {
            "status": "disconnected",
            "error": str(e),
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "message": "Application is running in MongoDB-only mode"
        }
    except Exception as e:
        logger.error(f"Error checking AWS Redis status: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "message": "Application is running in MongoDB-only mode"
        }
